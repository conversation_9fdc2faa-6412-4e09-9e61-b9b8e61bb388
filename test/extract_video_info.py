#!/usr/bin/env python3
"""
脚本用于从分割视频文件名中提取信息并生成JSON格式输出
文件名格式: 原视频名-开始时间-结束时间.mp4
例如: 20250727T075940Z_20250727T080440Z-00.01.01.023-00.01.03.193.mp4
"""

import os
import re
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional


def parse_time_to_seconds(time_str: str) -> float:
    """
    将时间字符串转换为秒数
    格式: 00.01.01.023 -> 小时.分钟.秒.毫秒
    """
    parts = time_str.split('.')
    if len(parts) != 4:
        raise ValueError(f"时间格式错误: {time_str}")
    
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = int(parts[2])
    milliseconds = int(parts[3])
    
    total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
    return total_seconds


def extract_video_info(filename: str) -> Optional[Tuple[str, float, float]]:
    """
    从视频文件名中提取信息
    返回: (原视频名, 开始时间秒数, 结束时间秒数)
    """
    # 移除.mp4扩展名
    name_without_ext = filename.replace('.mp4', '')
    
    # 使用正则表达式匹配文件名格式
    # 格式: 原视频名-开始时间-结束时间
    pattern = r'^(.+?)-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})$'
    match = re.match(pattern, name_without_ext)
    
    if not match:
        print(f"警告: 无法解析文件名格式: {filename}")
        return None
    
    original_video_name = match.group(1)
    start_time_str = match.group(2)
    end_time_str = match.group(3)
    
    try:
        start_time = parse_time_to_seconds(start_time_str)
        end_time = parse_time_to_seconds(end_time_str)
        return original_video_name, start_time, end_time
    except ValueError as e:
        print(f"警告: 时间解析错误 {filename}: {e}")
        return None


def get_label_from_directory(dir_name: str) -> str:
    """
    根据目录名确定标签
    """
    dir_lower = dir_name.lower()
    if 'nok_apperence' in dir_lower or 'nok_appearance' in dir_lower:
        return 'NOK_Appearance'
    elif 'nok_ele' in dir_lower:
        return 'NOK_Electrical'
    elif 'ok' in dir_lower:
        return 'OK_Action'
    else:
        return 'Unknown'


def calculate_optimal_feature_frame(duration_second: float, min_segment_length: float = 2.0) -> int:
    """
    根据视频时长和最小片段长度计算合适的feature_frame值

    Args:
        duration_second: 视频总时长（秒）
        min_segment_length: 最小动作片段长度（秒），默认2秒

    Returns:
        int: 建议的feature_frame值
    """
    # 目标：特征时间分辨率 = 最小片段长度的1/3到1/2
    target_resolution = min_segment_length / 3.0  # 0.67秒/特征点 for 2秒片段

    # 计算需要的特征点数
    feature_frame = int(duration_second / target_resolution)

    # 限制在合理范围内（最少100，最多1000）
    feature_frame = max(100, min(1000, feature_frame))

    return feature_frame


def process_segmented_videos(base_dir: str) -> Dict:
    """
    处理所有分割视频文件并生成JSON格式输出
    """
    base_path = Path(base_dir)
    result = {}
    
    # 遍历所有子目录
    for subdir in base_path.iterdir():
        if not subdir.is_dir():
            continue
            
        label = get_label_from_directory(subdir.name)
        print(f"处理目录: {subdir.name}, 标签: {label}")
        
        # 遍历目录中的所有mp4文件
        for video_file in subdir.glob('*.mp4'):
            video_info = extract_video_info(video_file.name)
            if video_info is None:
                continue
                
            original_video_name, start_time, end_time = video_info
            
            # 如果原视频名还没有在结果中，初始化它
            if original_video_name not in result:
                result[original_video_name] = {
                    "duration_second": 0.0,  # 需要根据实际情况设置
                    "duration_frame": 0,     # 需要根据实际情况设置
                    "annotations": [],
                    "feature_frame": 300,    # 提高特征密度，适合2-3秒的动作片段
                    "fps": 25.0,            # 默认值
                    "rfps": 25.0,           # 默认值
                    "original_video_path": f"/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos/{original_video_name}_decrypted_roi.mp4",
                    "segments_count": 0,
                    "feature_path": f"/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features_slowonly_reconstructed/{original_video_name}.csv"
                }
            
            # 添加注释段
            result[original_video_name]["annotations"].append({
                "segment": [start_time, end_time],
                "label": label
            })
            
            print(f"  添加段: {original_video_name} [{start_time:.3f}, {end_time:.3f}] -> {label}")
    
    # 更新segments_count并按时间排序annotations
    for video_name in result:
        # 按开始时间排序
        result[video_name]["annotations"].sort(key=lambda x: x["segment"][0])
        result[video_name]["segments_count"] = len(result[video_name]["annotations"])

        # 计算总时长（基于最后一个段的结束时间）
        if result[video_name]["annotations"]:
            max_end_time = max(ann["segment"][1] for ann in result[video_name]["annotations"])
            result[video_name]["duration_second"] = max_end_time
            result[video_name]["duration_frame"] = int(max_end_time * result[video_name]["fps"])

            # 动态计算合适的feature_frame值
            optimal_feature_frame = calculate_optimal_feature_frame(max_end_time)
            result[video_name]["feature_frame"] = optimal_feature_frame

            print(f"  视频 {video_name}: 时长={max_end_time:.1f}秒, feature_frame={optimal_feature_frame}, 时间分辨率={max_end_time/optimal_feature_frame:.2f}秒/特征点")
    
    return result


def main():
    """主函数"""
    # 设置输入和输出路径
    segmented_videos_dir = "/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos"
    output_file = "/home/<USER>/johnny_ws/mmaction2_ws/test/extracted_video_info.json"
    
    print(f"开始处理分割视频目录: {segmented_videos_dir}")
    
    # 检查输入目录是否存在
    if not os.path.exists(segmented_videos_dir):
        print(f"错误: 输入目录不存在: {segmented_videos_dir}")
        return
    
    # 处理视频文件
    result = process_segmented_videos(segmented_videos_dir)
    
    # 保存结果到JSON文件
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)
    
    print(f"\n处理完成!")
    print(f"共处理了 {len(result)} 个原始视频")
    print(f"结果已保存到: {output_file}")
    
    # 打印统计信息
    total_segments = sum(video_info["segments_count"] for video_info in result.values())
    print(f"总共提取了 {total_segments} 个视频段")
    
    # 按标签统计
    label_counts = {}
    for video_info in result.values():
        for annotation in video_info["annotations"]:
            label = annotation["label"]
            label_counts[label] = label_counts.get(label, 0) + 1
    
    print("\n标签统计:")
    for label, count in label_counts.items():
        print(f"  {label}: {count} 个段")


if __name__ == "__main__":
    main()
