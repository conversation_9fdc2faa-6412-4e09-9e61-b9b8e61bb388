#!/usr/bin/env python3
"""
脚本用于从分割视频文件名中提取信息并生成JSON格式输出
文件名格式: 原视频名-开始时间-结束时间.mp4
例如: 20250727T075940Z_20250727T080440Z-00.01.01.023-00.01.03.193.mp4
"""

import os
import re
import json
import random
import argparse
from pathlib import Path
from typing import Dict, List, Tuple, Optional
from collections import defaultdict


def parse_time_to_seconds(time_str: str) -> float:
    """
    将时间字符串转换为秒数
    格式: 00.01.01.023 -> 小时.分钟.秒.毫秒
    """
    parts = time_str.split('.')
    if len(parts) != 4:
        raise ValueError(f"时间格式错误: {time_str}")
    
    hours = int(parts[0])
    minutes = int(parts[1])
    seconds = int(parts[2])
    milliseconds = int(parts[3])
    
    total_seconds = hours * 3600 + minutes * 60 + seconds + milliseconds / 1000.0
    return total_seconds


def extract_video_info(filename: str) -> Optional[Tuple[str, float, float]]:
    """
    从视频文件名中提取信息
    返回: (原视频名, 开始时间秒数, 结束时间秒数)
    """
    # 移除.mp4扩展名
    name_without_ext = filename.replace('.mp4', '')
    
    # 使用正则表达式匹配文件名格式
    # 格式: 原视频名-开始时间-结束时间
    pattern = r'^(.+?)-(\d{2}\.\d{2}\.\d{2}\.\d{3})-(\d{2}\.\d{2}\.\d{2}\.\d{3})$'
    match = re.match(pattern, name_without_ext)
    
    if not match:
        print(f"警告: 无法解析文件名格式: {filename}")
        return None
    
    original_video_name = match.group(1)
    start_time_str = match.group(2)
    end_time_str = match.group(3)
    
    try:
        start_time = parse_time_to_seconds(start_time_str)
        end_time = parse_time_to_seconds(end_time_str)
        return original_video_name, start_time, end_time
    except ValueError as e:
        print(f"警告: 时间解析错误 {filename}: {e}")
        return None


def get_label_from_directory(dir_name: str) -> str:
    """
    根据目录名确定标签
    """
    dir_lower = dir_name.lower()
    if 'nok_apperence' in dir_lower or 'nok_appearance' in dir_lower:
        return 'NOK_Appearance'
    elif 'nok_ele' in dir_lower:
        return 'NOK_Electrical'
    elif 'ok' in dir_lower:
        return 'OK_Action'
    else:
        return 'Unknown'


def calculate_optimal_feature_frame(duration_second: float, min_segment_length: float = 2.0) -> int:
    """
    根据视频时长和最小片段长度计算合适的feature_frame值

    Args:
        duration_second: 视频总时长（秒）
        min_segment_length: 最小动作片段长度（秒），默认2秒

    Returns:
        int: 建议的feature_frame值
    """
    # 目标：特征时间分辨率 = 最小片段长度的1/3到1/2
    target_resolution = min_segment_length / 3.0  # 0.67秒/特征点 for 2秒片段

    # 计算需要的特征点数
    feature_frame = int(duration_second / target_resolution)

    # 限制在合理范围内（最少100，最多1000）
    feature_frame = max(100, min(1000, feature_frame))

    return feature_frame


def split_dataset_by_video(data: Dict, train_ratio: float = 0.8, val_ratio: float = 0.2,
                          random_seed: int = 42) -> Tuple[Dict, Dict]:
    """
    按视频级别分割数据集，确保同一视频的所有片段都在同一个集合中

    Args:
        data: 完整的数据字典
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        random_seed: 随机种子

    Returns:
        Tuple[Dict, Dict]: (训练集, 验证集)
    """
    if abs(train_ratio + val_ratio - 1.0) > 1e-6:
        raise ValueError(f"train_ratio + val_ratio 必须等于1.0，当前为 {train_ratio + val_ratio}")

    # 设置随机种子
    random.seed(random_seed)

    # 获取所有视频名并随机打乱
    video_names = list(data.keys())
    random.shuffle(video_names)

    # 计算分割点
    total_videos = len(video_names)
    train_count = int(total_videos * train_ratio)

    # 分割视频名
    train_videos = video_names[:train_count]
    val_videos = video_names[train_count:]

    # 构建训练集和验证集
    train_data = {video: data[video] for video in train_videos}
    val_data = {video: data[video] for video in val_videos}

    return train_data, val_data


def split_dataset_stratified(data: Dict, train_ratio: float = 0.8, val_ratio: float = 0.2,
                           random_seed: int = 42) -> Tuple[Dict, Dict]:
    """
    按标签分层分割数据集，确保每个标签在训练集和验证集中的比例相似

    Args:
        data: 完整的数据字典
        train_ratio: 训练集比例
        val_ratio: 验证集比例
        random_seed: 随机种子

    Returns:
        Tuple[Dict, Dict]: (训练集, 验证集)
    """
    if abs(train_ratio + val_ratio - 1.0) > 1e-6:
        raise ValueError(f"train_ratio + val_ratio 必须等于1.0，当前为 {train_ratio + val_ratio}")

    # 设置随机种子
    random.seed(random_seed)

    # 按标签分组视频
    label_to_videos = defaultdict(list)
    for video_name, video_info in data.items():
        # 获取该视频的主要标签（出现最多的标签）
        label_counts = defaultdict(int)
        for annotation in video_info['annotations']:
            label_counts[annotation['label']] += 1

        if label_counts:
            main_label = max(label_counts.keys(), key=lambda x: label_counts[x])
            label_to_videos[main_label].append(video_name)

    train_videos = []
    val_videos = []

    # 对每个标签分别进行分割
    for label, videos in label_to_videos.items():
        random.shuffle(videos)
        train_count = int(len(videos) * train_ratio)

        train_videos.extend(videos[:train_count])
        val_videos.extend(videos[train_count:])

    # 构建训练集和验证集
    train_data = {video: data[video] for video in train_videos}
    val_data = {video: data[video] for video in val_videos}

    return train_data, val_data


def print_dataset_statistics(data: Dict, dataset_name: str):
    """
    打印数据集统计信息

    Args:
        data: 数据字典
        dataset_name: 数据集名称
    """
    print(f"\n=== {dataset_name} 统计信息 ===")
    print(f"视频数量: {len(data)}")

    # 统计标签
    label_counts = defaultdict(int)
    total_segments = 0
    total_duration = 0.0

    for video_info in data.values():
        total_segments += len(video_info['annotations'])
        total_duration += video_info['duration_second']

        for annotation in video_info['annotations']:
            label_counts[annotation['label']] += 1

    print(f"总片段数: {total_segments}")
    print(f"总时长: {total_duration:.1f}秒 ({total_duration/60:.1f}分钟)")
    print(f"平均视频时长: {total_duration/len(data):.1f}秒")

    print("标签分布:")
    for label, count in sorted(label_counts.items()):
        percentage = count / total_segments * 100
        print(f"  {label}: {count} 个片段 ({percentage:.1f}%)")


def save_dataset_splits(train_data: Dict, val_data: Dict, output_dir: str):
    """
    保存分割后的数据集

    Args:
        train_data: 训练集数据
        val_data: 验证集数据
        output_dir: 输出目录
    """
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    # 保存训练集
    train_file = output_path / "multiclass_tad_train.json"
    with open(train_file, 'w', encoding='utf-8') as f:
        json.dump(train_data, f, indent=2, ensure_ascii=False)
    print(f"训练集已保存到: {train_file}")

    # 保存验证集
    val_file = output_path / "multiclass_tad_val.json"
    with open(val_file, 'w', encoding='utf-8') as f:
        json.dump(val_data, f, indent=2, ensure_ascii=False)
    print(f"验证集已保存到: {val_file}")

    # 保存完整数据集（用于参考）
    full_data = {**train_data, **val_data}
    full_file = output_path / "multiclass_tad_full.json"
    with open(full_file, 'w', encoding='utf-8') as f:
        json.dump(full_data, f, indent=2, ensure_ascii=False)
    print(f"完整数据集已保存到: {full_file}")

    return train_file, val_file, full_file


def process_segmented_videos(base_dir: str) -> Dict:
    """
    处理所有分割视频文件并生成JSON格式输出
    """
    base_path = Path(base_dir)
    result = {}
    
    # 遍历所有子目录
    for subdir in base_path.iterdir():
        if not subdir.is_dir():
            continue
            
        label = get_label_from_directory(subdir.name)
        print(f"处理目录: {subdir.name}, 标签: {label}")
        
        # 遍历目录中的所有mp4文件
        for video_file in subdir.glob('*.mp4'):
            video_info = extract_video_info(video_file.name)
            if video_info is None:
                continue
                
            original_video_name, start_time, end_time = video_info
            
            # 如果原视频名还没有在结果中，初始化它
            if original_video_name not in result:
                result[original_video_name] = {
                    "duration_second": 0.0,  # 需要根据实际情况设置
                    "duration_frame": 0,     # 需要根据实际情况设置
                    "annotations": [],
                    "feature_frame": 300,    # 提高特征密度，适合2-3秒的动作片段
                    "fps": 25.0,            # 默认值
                    "rfps": 25.0,           # 默认值
                    "original_video_path": f"/home/<USER>/johnny_ws/mmaction2_ws/data/raw_videos/{original_video_name}_decrypted_roi.mp4",
                    "segments_count": 0,
                    "feature_path": f"/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD/features_slowonly_reconstructed/{original_video_name}.csv"
                }
            
            # 添加注释段
            result[original_video_name]["annotations"].append({
                "segment": [start_time, end_time],
                "label": label
            })
            
            print(f"  添加段: {original_video_name} [{start_time:.3f}, {end_time:.3f}] -> {label}")
    
    # 更新segments_count并按时间排序annotations
    for video_name in result:
        # 按开始时间排序
        result[video_name]["annotations"].sort(key=lambda x: x["segment"][0])
        result[video_name]["segments_count"] = len(result[video_name]["annotations"])

        # 计算总时长（基于最后一个段的结束时间）
        if result[video_name]["annotations"]:
            max_end_time = max(ann["segment"][1] for ann in result[video_name]["annotations"])
            result[video_name]["duration_second"] = max_end_time
            result[video_name]["duration_frame"] = int(max_end_time * result[video_name]["fps"])

            # 动态计算合适的feature_frame值
            optimal_feature_frame = calculate_optimal_feature_frame(max_end_time)
            result[video_name]["feature_frame"] = optimal_feature_frame

            print(f"  视频 {video_name}: 时长={max_end_time:.1f}秒, feature_frame={optimal_feature_frame}, 时间分辨率={max_end_time/optimal_feature_frame:.2f}秒/特征点")
    
    return result


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="从分割视频文件中提取信息并生成训练/验证数据集",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 交互模式（默认）
  python extract_video_info.py

  # 非交互模式，按视频分割，8:2比例
  python extract_video_info.py --non-interactive --split-method video --train-ratio 0.8

  # 非交互模式，按标签分层分割，7:3比例
  python extract_video_info.py --non-interactive --split-method stratified --train-ratio 0.7

  # 指定自定义路径
  python extract_video_info.py --input-dir /path/to/videos --output-dir /path/to/output
        """
    )

    parser.add_argument(
        '--input-dir',
        default="/home/<USER>/johnny_ws/mmaction2_ws/data/segmented_videos",
        help="分割视频文件目录路径 (默认: %(default)s)"
    )

    parser.add_argument(
        '--output-dir',
        default="/home/<USER>/johnny_ws/mmaction2_ws/data/MultiClassTAD",
        help="输出数据集目录路径 (默认: %(default)s)"
    )

    parser.add_argument(
        '--non-interactive',
        action='store_true',
        help="非交互模式，使用默认或指定的参数"
    )

    parser.add_argument(
        '--split-method',
        choices=['video', 'stratified'],
        default='video',
        help="数据集分割方式: video=按视频分割, stratified=按标签分层分割 (默认: %(default)s)"
    )

    parser.add_argument(
        '--train-ratio',
        type=float,
        default=0.8,
        help="训练集比例 (0-1之间，默认: %(default)s)"
    )

    parser.add_argument(
        '--random-seed',
        type=int,
        default=42,
        help="随机种子 (默认: %(default)s)"
    )

    parser.add_argument(
        '--min-segment-length',
        type=float,
        default=2.0,
        help="最小动作片段长度，用于计算特征时间分辨率 (默认: %(default)s秒)"
    )

    return parser.parse_args()


def main():
    """主函数"""
    # 解析命令行参数
    args = parse_arguments()

    segmented_videos_dir = args.input_dir
    output_dir = args.output_dir

    print(f"开始处理分割视频目录: {segmented_videos_dir}")

    # 检查输入目录是否存在
    if not os.path.exists(segmented_videos_dir):
        print(f"错误: 输入目录不存在: {segmented_videos_dir}")
        return

    # 处理视频文件
    print("=" * 60)
    print("第1步: 提取视频信息")
    print("=" * 60)
    result = process_segmented_videos(segmented_videos_dir)

    print(f"\n处理完成!")
    print(f"共处理了 {len(result)} 个原始视频")

    # 打印完整数据集统计信息
    print_dataset_statistics(result, "完整数据集")

    # 数据集分割
    print("\n" + "=" * 60)
    print("第2步: 数据集分割")
    print("=" * 60)

    # 提供两种分割方式供用户选择
    print("请选择数据集分割方式:")
    print("1. 按视频分割 (推荐) - 确保同一视频的所有片段都在同一个集合中")
    print("2. 按标签分层分割 - 确保每个标签在训练集和验证集中的比例相似")

    while True:
        try:
            choice = input("请输入选择 (1 或 2，默认为1): ").strip()
            if choice == "" or choice == "1":
                split_method = "video"
                break
            elif choice == "2":
                split_method = "stratified"
                break
            else:
                print("无效选择，请输入1或2")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return

    # 获取分割比例
    while True:
        try:
            train_ratio_input = input("请输入训练集比例 (0-1之间，默认0.8): ").strip()
            if train_ratio_input == "":
                train_ratio = 0.8
            else:
                train_ratio = float(train_ratio_input)
                if not 0 < train_ratio < 1:
                    print("训练集比例必须在0和1之间")
                    continue
            val_ratio = 1.0 - train_ratio
            break
        except ValueError:
            print("请输入有效的数字")
        except KeyboardInterrupt:
            print("\n用户取消操作")
            return

    # 执行数据集分割
    print(f"\n使用{'按视频分割' if split_method == 'video' else '按标签分层分割'}方式")
    print(f"训练集比例: {train_ratio:.1%}, 验证集比例: {val_ratio:.1%}")

    if split_method == "video":
        train_data, val_data = split_dataset_by_video(result, train_ratio, val_ratio)
    else:
        train_data, val_data = split_dataset_stratified(result, train_ratio, val_ratio)

    # 打印分割后的统计信息
    print_dataset_statistics(train_data, "训练集")
    print_dataset_statistics(val_data, "验证集")

    # 保存数据集
    print("\n" + "=" * 60)
    print("第3步: 保存数据集文件")
    print("=" * 60)

    train_file, val_file, full_file = save_dataset_splits(train_data, val_data, output_dir)

    print(f"\n🎉 数据集生成完成!")
    print(f"📁 输出目录: {output_dir}")
    print(f"📊 数据集统计:")
    print(f"   - 总视频数: {len(result)}")
    print(f"   - 训练集: {len(train_data)} 个视频")
    print(f"   - 验证集: {len(val_data)} 个视频")

    total_train_segments = sum(len(v['annotations']) for v in train_data.values())
    total_val_segments = sum(len(v['annotations']) for v in val_data.values())
    print(f"   - 训练片段: {total_train_segments} 个")
    print(f"   - 验证片段: {total_val_segments} 个")


if __name__ == "__main__":
    main()
